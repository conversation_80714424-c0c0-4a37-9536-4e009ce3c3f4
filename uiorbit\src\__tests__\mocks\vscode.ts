/**
 * Mock implementation of VS Code API for unit testing
 * This provides a comprehensive mock of the vscode module
 */

// Mock URI class
class MockUri {
  constructor(
    public scheme: string = 'file',
    public authority: string = '',
    public path: string = '',
    public query: string = '',
    public fragment: string = ''
  ) {}

  get fsPath(): string {
    return this.path;
  }

  static file(path: string): Mock<PERSON><PERSON> {
    return new Mock<PERSON>ri('file', '', path);
  }

  static parse(value: string): Mock<PERSON><PERSON> {
    return new <PERSON>ck<PERSON>ri('file', '', value);
  }

  toString(): string {
    return `${this.scheme}://${this.authority}${this.path}`;
  }

  toJSON(): any {
    return {
      scheme: this.scheme,
      authority: this.authority,
      path: this.path,
      query: this.query,
      fragment: this.fragment,
    };
  }
}

// Mock Range class
class MockRange {
  constructor(
    public start: { line: number; character: number },
    public end: { line: number; character: number }
  ) {}

  get isEmpty(): boolean {
    return this.start.line === this.end.line && this.start.character === this.end.character;
  }

  contains(positionOrRange: any): boolean {
    return false; // Simplified mock
  }
}

// Mock Position class
class MockPosition {
  constructor(public line: number, public character: number) {}

  isAfter(other: MockPosition): boolean {
    return this.line > other.line || (this.line === other.line && this.character > other.character);
  }

  isBefore(other: MockPosition): boolean {
    return this.line < other.line || (this.line === other.line && this.character < other.character);
  }
}

// Mock Disposable class
class MockDisposable {
  constructor(private callOnDispose?: () => void) {}

  dispose(): void {
    if (this.callOnDispose) {
      this.callOnDispose();
    }
  }

  static from(...disposables: MockDisposable[]): MockDisposable {
    return new MockDisposable(() => {
      disposables.forEach(d => d.dispose());
    });
  }
}

// Mock EventEmitter
class MockEventEmitter<T> {
  private listeners: ((e: T) => void)[] = [];

  get event() {
    return (listener: (e: T) => void) => {
      this.listeners.push(listener);
      return new MockDisposable(() => {
        const index = this.listeners.indexOf(listener);
        if (index >= 0) {
          this.listeners.splice(index, 1);
        }
      });
    };
  }

  fire(data: T): void {
    this.listeners.forEach(listener => listener(data));
  }

  dispose(): void {
    this.listeners = [];
  }
}

// Mock workspace
const workspace = {
  workspaceFolders: [
    {
      uri: MockUri.file('/mock/workspace'),
      name: 'test-workspace',
      index: 0,
    },
  ],
  getConfiguration: jest.fn(() => ({
    get: jest.fn(),
    update: jest.fn(),
    has: jest.fn(),
    inspect: jest.fn(),
  })),
  onDidChangeConfiguration: jest.fn(() => new MockDisposable()),
  onDidChangeWorkspaceFolders: jest.fn(() => new MockDisposable()),
  findFiles: jest.fn(() => Promise.resolve([])),
  openTextDocument: jest.fn(() => Promise.resolve({})),
  saveAll: jest.fn(() => Promise.resolve(true)),
  fs: {
    readFile: jest.fn(),
    writeFile: jest.fn(),
    createDirectory: jest.fn(),
    delete: jest.fn(),
    stat: jest.fn(),
    readDirectory: jest.fn(),
  },
  createFileSystemWatcher: jest.fn(() => ({
    onDidCreate: jest.fn(() => new MockDisposable()),
    onDidChange: jest.fn(() => new MockDisposable()),
    onDidDelete: jest.fn(() => new MockDisposable()),
    dispose: jest.fn(),
  })),
};

// Mock window
const window = {
  showInformationMessage: jest.fn(() => Promise.resolve()),
  showWarningMessage: jest.fn(() => Promise.resolve()),
  showErrorMessage: jest.fn(() => Promise.resolve()),
  showQuickPick: jest.fn(() => Promise.resolve()),
  showInputBox: jest.fn(() => Promise.resolve()),
  createOutputChannel: jest.fn(() => ({
    append: jest.fn(),
    appendLine: jest.fn(),
    clear: jest.fn(),
    show: jest.fn(),
    hide: jest.fn(),
    dispose: jest.fn(),
  })),
  createTerminal: jest.fn(() => ({
    sendText: jest.fn(),
    show: jest.fn(),
    hide: jest.fn(),
    dispose: jest.fn(),
  })),
  registerWebviewViewProvider: jest.fn(() => new MockDisposable()),
  activeTextEditor: undefined,
  visibleTextEditors: [],
  onDidChangeActiveTextEditor: jest.fn(() => new MockDisposable()),
  onDidChangeVisibleTextEditors: jest.fn(() => new MockDisposable()),
};

// Mock commands
const commands = {
  registerCommand: jest.fn(() => new MockDisposable()),
  executeCommand: jest.fn(() => Promise.resolve()),
  getCommands: jest.fn(() => Promise.resolve([])),
};

// Mock extensions
const extensions = {
  getExtension: jest.fn(),
  all: [],
  onDidChange: jest.fn(() => new MockDisposable()),
};

// Export the mock
export = {
  Uri: MockUri,
  Range: MockRange,
  Position: MockPosition,
  Disposable: MockDisposable,
  EventEmitter: MockEventEmitter,
  workspace,
  window,
  commands,
  extensions,
  
  // Enums and constants
  ExtensionMode: {
    Development: 1,
    Test: 2,
    Production: 3,
  },
  
  ViewColumn: {
    Active: -1,
    Beside: -2,
    One: 1,
    Two: 2,
    Three: 3,
  },
  
  // Mock webview API
  WebviewPanel: class MockWebviewPanel {
    constructor(
      public viewType: string,
      public title: string,
      public viewColumn: number,
      public options: any
    ) {}
    
    dispose = jest.fn();
    reveal = jest.fn();
    webview = {
      html: '',
      postMessage: jest.fn(),
      onDidReceiveMessage: jest.fn(() => new MockDisposable()),
      asWebviewUri: jest.fn((uri: any) => uri),
      cspSource: 'vscode-webview:',
    };
  },
};
