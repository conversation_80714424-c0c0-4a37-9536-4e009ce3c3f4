{"name": "uiorbit", "displayName": "UIOrbit - AI Frontend Developer", "description": "World-class AI assistant for modern UI/UX development with codebase understanding", "version": "1.0.0", "publisher": "uiorbit", "engines": {"vscode": "^1.85.0"}, "categories": ["Other", "Machine Learning", "Snippets", "Formatters"], "keywords": ["ai", "frontend", "ui", "ux", "react", "vue", "angular", "svelte", "tailwind", "gsap", "assistant", "code-generation"], "activationEvents": ["onStartupFinished"], "main": "./dist/extension.js", "contributes": {"views": {"explorer": [{"type": "webview", "id": "uiorbit.chatView", "name": "UIOrbit Chat", "when": "true"}]}, "commands": [{"command": "uiorbit.openChat", "title": "Open UIOrbit Chat", "category": "UIOrbit"}, {"command": "uiorbit.generateComponent", "title": "Generate Component", "category": "UIOrbit"}, {"command": "uiorbit.analyzeCode", "title": "Analyze Code", "category": "UIOrbit"}], "menus": {"explorer/context": [{"command": "uiorbit.generateComponent", "group": "uiorbit", "when": "explorerResourceIsFolder"}], "editor/context": [{"command": "uiorbit.analyzeCode", "group": "uiorbit", "when": "editorHasSelection"}]}, "configuration": {"title": "UIOrbit", "properties": {"uiorbit.openaiApiKey": {"type": "string", "default": "", "description": "OpenAI API Key for AI functionality", "scope": "application"}, "uiorbit.defaultFramework": {"type": "string", "enum": ["react", "vue", "angular", "svelte", "vanilla"], "default": "react", "description": "Default frontend framework for code generation"}, "uiorbit.defaultStyling": {"type": "string", "enum": ["tailwind", "css", "scss", "styled-components", "emotion"], "default": "tailwind", "description": "Default styling approach for components"}, "uiorbit.enableAccessibility": {"type": "boolean", "default": true, "description": "Enable accessibility features in generated code"}, "uiorbit.enableResponsiveDesign": {"type": "boolean", "default": true, "description": "Enable responsive design in generated components"}, "uiorbit.debugMode": {"type": "boolean", "default": false, "description": "Enable debug mode for development"}}}}, "scripts": {"vscode:prepublish": "npm run package", "compile": "webpack", "watch": "webpack --watch", "package": "webpack --mode production --devtool hidden-source-map", "compile-tests": "tsc -p . --outDir out", "watch-tests": "tsc -p . -w --outDir out", "pretest": "npm run compile-tests && npm run compile && npm run lint", "lint": "eslint src", "test": "vscode-test", "test:unit": "jest", "test:unit:watch": "jest --watch", "test:unit:coverage": "jest --coverage", "test:integration": "npm run compile-tests && vscode-test", "test:all": "npm run test:unit && npm run test:integration", "ci": "npm run lint && npm run test:unit:coverage && npm run test:integration"}, "devDependencies": {"@types/vscode": "^1.85.0", "@types/mocha": "^10.0.10", "@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "prettier": "^3.1.1", "typescript": "^5.3.3", "ts-loader": "^9.5.1", "webpack": "^5.89.0", "webpack-cli": "^5.1.4", "@vscode/test-cli": "^0.0.10", "@vscode/test-electron": "^2.3.8", "jest": "^29.7.0", "@types/jest": "^29.5.11", "ts-jest": "^29.1.1", "@types/fs-extra": "^11.0.4", "@types/lodash": "^4.14.202"}, "dependencies": {"openai": "^4.24.1", "dotenv": "^16.3.1", "fs-extra": "^11.2.0", "glob": "^10.3.10", "chokidar": "^3.5.3", "lodash": "^4.17.21"}}